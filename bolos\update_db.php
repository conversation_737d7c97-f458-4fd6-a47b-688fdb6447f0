<?php
// Conectar ao banco de dados
$host = 'localhost';
$dbname = 'loja_bolos';
$user = 'root';
$pass = '123456789';

try {
    $pdo = new PDO("mysql:host=$host;dbname=$dbname", $user, $pass);
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    
    // Verificar o nome exato da coluna no banco de dados
    $stmt = $pdo->query("DESCRIBE utilizadores");
    $columns = $stmt->fetchAll(PDO::FETCH_COLUMN);
    
    echo "<h2>Colunas existentes na tabela 'utilizadores':</h2>";
    echo "<ul>";
    foreach ($columns as $column) {
        echo "<li>" . htmlspecialchars($column) . "</li>";
    }
    echo "</ul>";
    
    // Verificar se a coluna 'codigo_postal' existe
    $codigo_postal_exists = false;
    foreach ($columns as $column) {
        if (strtolower($column) === 'codigo_postal') {
            $codigo_postal_exists = true;
            break;
        }
    }
    
    // Se a coluna não existir, adicioná-la
    if (!$codigo_postal_exists) {
        $pdo->exec("ALTER TABLE utilizadores ADD COLUMN codigo_postal varchar(20) DEFAULT NULL");
        echo "<p style='color: green;'>Coluna 'codigo_postal' adicionada com sucesso!</p>";
    } else {
        echo "<p>A coluna 'codigo_postal' já existe.</p>";
    }
    
    // Verificar se a coluna 'role' existe
    $role_exists = false;
    foreach ($columns as $column) {
        if (strtolower($column) === 'role') {
            $role_exists = true;
            break;
        }
    }
    
    // Se a coluna não existir, adicioná-la
    if (!$role_exists) {
        $pdo->exec("ALTER TABLE utilizadores ADD COLUMN role varchar(20) NOT NULL DEFAULT 'cliente'");
        echo "<p style='color: green;'>Coluna 'role' adicionada com sucesso!</p>";
    } else {
        echo "<p>A coluna 'role' já existe.</p>";
    }
    
    echo "<p style='color: blue;'>Estrutura da tabela atualizada. Tente registrar um usuário novamente.</p>";
    
} catch(PDOException $e) {
    die("<p style='color: red;'>Erro na atualização do banco de dados: " . $e->getMessage() . "</p>");
}
?>

<p><a href="registo.php" style="display: inline-block; padding: 10px 20px; background-color: #93622B; color: white; text-decoration: none; border-radius: 5px; margin-top: 20px;">Voltar para a página de registro</a></p>