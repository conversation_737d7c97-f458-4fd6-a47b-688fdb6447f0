*{
       margin: 0;
       padding: 0;
       
}

a{
      font-family: 'Playfair Display';
      

}

body{
       background:white;
       height: 6000px;
}

.menu-bar{
       background: #B0C4DE;
       text-align: center;
       list-style: none;
}

.menu-bar ul{
       display: inline-flex;
       color: #1a282e;
       
}

.menu-bar ul li{
       width: 120px;
       margin: 25px;
       padding: 15px;

}

.menu-bar ul li a{
       text-decoration: none;
       color: #1a282e;
       gap: 5px;

}
.active, .menu-bar ul li a:hover{
       background: #B0E0E6;
       border-radius: 3px;
       padding: 5px;
       height: 110px;

}

.searche{
       padding: 25px;
       width: 20px;
      margin: 40px;
      padding-right: 25px;
}


.barra{
       display: flex;
       justify-content: space-between;
       height: 110px;
}


.nav-list-icon{
       display: flex;
       margin: 45px;
       gap: 100px;

}




.fa-cart-plus{
       color:#858686; 
       font-size: 30px;
      
}

.fa-cart-plus:hover{
       color: #1a282e; 
       font-size: 35px;
    
}

.fa-user{
       color: #858686; 
       font-size: 30px;
}

.fa-user:hover{
       color: #1a282e; 
       font-size: 35px;
}

.nav-list{
       
       font-family: 'Playfair Display';
       display: flex;
       list-style: none;
      
       margin-top: 50px;
       padding-right: 150px;
       text-align: center;
       
       
}

.nav-list-icon{
       margin-top: 50px;
       gap: 32px;
       justify-content: space-between;
       


} 

.nav-list a:hover{
       color: #8b4a22;
       font-size: 35 px;



}


.nav{
       display: flex;
       justify-content: space-between;
       max-width:1280px ;
       margin-inline:auto ;
       padding-block: 16px;
       border-radius: 8px;
       
       
}

.header{
       background: #a3c6d4;
}

.menu-bar{
       height: 100px;
}

