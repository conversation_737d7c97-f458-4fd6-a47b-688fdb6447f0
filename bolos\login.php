<?php
    include 'db.php';
    session_start();
    $message = '';
    
    // Se o usuário já estiver logado, redireciona
    if (isset($_SESSION['user_id'])) {
        header("Location: index.php");
        exit;
    }

    if ($_SERVER['REQUEST_METHOD'] === 'POST'){
        $email = $_POST['email'] ?? '';
        $senha = $_POST['senha'] ?? '';

        // Verifica se os campos foram preenchidos
        if (empty($email) || empty($senha)) {
            $message = "Por favor, preencha todos os campos.";
        } else {
            try {
                // Consulta o banco de dados
                $stmt = $pdo->prepare("SELECT * FROM utilizadores WHERE email = ?");
                $stmt->execute([$email]);
                $user = $stmt->fetch();
                
                if ($user && password_verify($senha, $user['senha'])) {
                    // Login bem-sucedido
                    $_SESSION['user_id'] = $user['id_utilizador'];
                    $_SESSION['user_name'] = $user['nome'];
                    $_SESSION['user_email'] = $user['email'];
                    $_SESSION['user_role'] = $user['role'];
                    
                    // Redireciona com base no tipo de usuário
                    if ($user['role'] === 'admin') {
                        header("Location: admin/dashboard.php");
                    } else {
                        header("Location: index.php");
                    }
                    exit;
                } else {
                    // Login falhou
                    $message = "Email ou senha incorretos.";
                }
            } catch (PDOException $e) {
                $message = "Erro ao fazer login: " . $e->getMessage();
            }
        } 
    }
?>

<!DOCTYPE html>
<html lang="pt">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Login - Cake Garden</title>
    <link rel="stylesheet" href="../css/estilo.css">
    <!-- Font Awesome para ícones -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.5.1/css/all.min.css">
</head>
<body>
    <div class="login">
        <div class="form-box" id="login">
            <form action="" method="post">
                <h2>Login</h2>
                
                <?php if (!empty($message)): ?>
                    <p class="message error"><?= htmlspecialchars($message) ?></p>
                <?php endif; ?>
                
                <!-- Campo Email -->
                <div class="input-group">
                    <i class="fa-solid fa-envelope"></i>
                    <input type="email" name="email" placeholder="Email" required>
                </div>
                
                <!-- Campo Senha -->
                <div class="input-group">
                    <i class="fa-solid fa-lock"></i>
                    <input type="password" name="senha" placeholder="Senha" required>
                </div>
                
                <button type="submit" class="btn">Login</button>
                <p>Não tem conta? <a href="registo.php">Registe-se</a></p>
            </form>
        </div>
    </div>
</body>
</html>
