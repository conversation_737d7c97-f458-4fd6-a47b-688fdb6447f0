<?php
    include '../db.php';
    session_start();
    
    // Verifica se o usuário está logado e é admin
    if (!isset($_SESSION['user_id']) || $_SESSION['user_role'] !== 'admin') {
        header("Location: ../login.php");
        exit;
    }
    
    // Busca informações para o dashboard
    $stmt = $pdo->query("SELECT COUNT(*) as total FROM utilizadores WHERE role = 'cliente'");
    $total_clientes = $stmt->fetch()['total'];
    
    $stmt = $pdo->query("SELECT COUNT(*) as total FROM pedidos");
    $total_pedidos = $stmt->fetch()['total'];
    
    $stmt = $pdo->query("SELECT COUNT(*) as total FROM bolo");
    $total_bolos = $stmt->fetch()['total'];
?>

<!DOCTYPE html>
<html lang="pt">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Dashboard Admin - Cake Garden</title>
    <link rel="stylesheet" href="../../css/admin.css">
    <!-- Font Awesome para ícones -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.5.1/css/all.min.css">
</head>
<body>
    <div class="admin-container">
        <!-- Sidebar -->
        <div class="sidebar">
            <div class="logo">
                <h2>Cake Garden</h2>
                <p>Painel Admin</p>
            </div>
            <ul class="menu">
                <li class="active"><a href="dashboard.php"><i class="fa-solid fa-gauge"></i> Dashboard</a></li>
                <li><a href="bolos.php"><i class="fa-solid fa-cake-candles"></i> Gerir Bolos</a></li>
                <li><a href="pedidos.php"><i class="fa-solid fa-shopping-cart"></i> Pedidos</a></li>
                <li><a href="clientes.php"><i class="fa-solid fa-users"></i> Clientes</a></li>
                <li><a href="../index.php"><i class="fa-solid fa-home"></i> Ver Site</a></li>
                <li><a href="../logout.php"><i class="fa-solid fa-sign-out-alt"></i> Sair</a></li>
            </ul>
        </div>
        
        <!-- Conteúdo principal -->
        <div class="main-content">
            <div class="header">
                <h1>Dashboard</h1>
                <div class="user-info">
                    <span>Bem-vindo, <?php echo htmlspecialchars($_SESSION['user_name']); ?></span>
                    <a href="../logout.php" class="logout-btn"><i class="fa-solid fa-sign-out-alt"></i></a>
                </div>
            </div>
            
            <!-- Cards de estatísticas -->
            <div class="stats-container">
                <div class="stat-card">
                    <div class="stat-icon"><i class="fa-solid fa-users"></i></div>
                    <div class="stat-info">
                        <h3>Total de Clientes</h3>
                        <p><?php echo $total_clientes; ?></p>
                    </div>
                </div>
                
                <div class="stat-card">
                    <div class="stat-icon"><i class="fa-solid fa-shopping-cart"></i></div>
                    <div class="stat-info">
                        <h3>Total de Pedidos</h3>
                        <p><?php echo $total_pedidos; ?></p>
                    </div>
                </div>
                
                <div class="stat-card">
                    <div class="stat-icon"><i class="fa-solid fa-cake-candles"></i></div>
                    <div class="stat-info">
                        <h3>Bolos Cadastrados</h3>
                        <p><?php echo $total_bolos; ?></p>
                    </div>
                </div>
            </div>
            
            <!-- Ações rápidas -->
            <div class="quick-actions">
                <h2>Ações Rápidas</h2>
                <div class="actions-container">
                    <a href="novo-bolo.php" class="action-card">
                        <i class="fa-solid fa-plus"></i>
                        <span>Adicionar Bolo</span>
                    </a>
                    
                    <a href="pedidos.php?status=novo" class="action-card">
                        <i class="fa-solid fa-list-check"></i>
                        <span>Ver Novos Pedidos</span>
                    </a>
                    
                    <a href="relatorios.php" class="action-card">
                        <i class="fa-solid fa-chart-line"></i>
                        <span>Gerar Relatórios</span>
                    </a>
                </div>
            </div>
        </div>
    </div>
</body>
</html>